package log320;

import java.util.ArrayList;

import static log320.Const.ALL_MOVES;
import static log320.Const.RANDOM;

public class Game {
    private static Game instance;

    private Board board;
    private CPUPlayer cpuPlayer;
    private Player currentPlayer;

    private Game() {
    }

    public static Game getInstance() {
        if (instance == null) {
            instance = new Game();
        }

        return instance;
    }

    public void start(String boardState, Player currentPlayer) {
        this.board = new Board(boardState);
        this.cpuPlayer = new CPUPlayer(board, currentPlayer);
    }

    public Move getNextMove() {
        ArrayList<Move> moves = cpuPlayer.getNextMove();
        Move move = moves.get(RANDOM.nextInt(moves.size()));
        board.play(move);
        return move;
    }

    public Player getPlayer() {
        return currentPlayer;
    }

    public void play(Move move) {
        board.play(move);
    }

    public void play(String moveString) {
        Move move = ALL_MOVES.get(moveString);
        board.play(move);
    }

    public void printBoard() {
        board.print();
    }

    public Move getLastMove() {
        return board.getLastMove();
    }
}
