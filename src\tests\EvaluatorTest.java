package tests;

import log320.Board;
import log320.Const;
import log320.Player;

import static log320.Const.LOSS_SCORE;
import static log320.Const.WIN_SCORE;

public class EvaluatorTest {
    public static void main(String[] args) {
        Board board = new Board();
        board.reset();
        board.play(Const.ALL_MOVES.get("D2E3"));
        board.play(Const.ALL_MOVES.get("E7F6"));
        int score = board.evaluate(Player.RED);

        System.out.println("Score for RED after moves D2E3 and E7F6: " + score);

        board.clear();
        for (int i = 0; i < 8; i++) {
            board.place(i, 6, Player.RED.getPusher());
            System.out.println(board.evaluate(Player.RED) == WIN_SCORE);
        }

        board.clear();
        for (int i = 0; i < 8; i++) {
            board.place(i, 0, Player.BLACK.getPusher());
            System.out.println(board.evaluate(Player.RED) == LOSS_SCORE);
        }

        board.clear();
        board.place(0, 5, Player.RED.getPusher());
        System.out.println(board.evaluate(Player.RED) == WIN_SCORE);

        board.clear();
        board.place(0, 5, Player.BLACK.getPusher());
        System.out.println(board.evaluate(Player.RED) == LOSS_SCORE);
    }
}
