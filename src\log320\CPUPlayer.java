package log320;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import static log320.Const.*;

public class CPUPlayer {
    private final Board BOARD;
    private final Player PLAYER;
    private final ArrayList<Move> BEST_MOVES = new ArrayList<>(20);
    private final ArrayList<Move> CURRENT_BEST_MOVES = new ArrayList<>(20);

    public CPUPlayer(Board board, Player player) {
        this.BOARD = board;
        this.PLAYER = player;
    }

    // Retourne la liste des coups possibles.  Cette liste contient
    // plusieurs coups possibles si et seuleument si plusieurs coups
    // ont le même score.
    public ArrayList<Move> getNextMove() {
        long startTime = System.currentTimeMillis();
        BEST_MOVES.clear();
        int maxDepth = MAX_DEPTH;
        int bestScore;
        List<Move> possibleMoves = BOARD.getPossibleMoves(PLAYER);
        ExecutorService executor;
        List<Future<int[]>> futures;

        Move winningMove = possibleMoves.stream().filter(Move::isWinning).findAny().orElse(null);
        if (winningMove != null) {
            BEST_MOVES.add(winningMove);
            return BEST_MOVES;
        }

        while (System.currentTimeMillis() - startTime < MAX_TIME_MILLIS) {
            CURRENT_BEST_MOVES.clear();
            bestScore = Integer.MIN_VALUE;
            possibleMoves = BOARD.getPossibleMoves(PLAYER);
            executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            futures = new ArrayList<>();

            for (int moveIndex = 0; moveIndex < possibleMoves.size(); moveIndex++) {
                Move move = possibleMoves.get(moveIndex);
                Board boardCopy = BOARD.clone();
                boardCopy.play(move);

                final int idx = moveIndex;
                int finalMaxDepth = maxDepth;
                futures.add(executor.submit(() -> {
                    int score = negamax(
                            PLAYER.getOpponent(),
                            boardCopy,
                            Integer.MIN_VALUE,
                            Integer.MAX_VALUE,
                            finalMaxDepth,
                            startTime
                    );
                    return new int[]{score, idx};
                }));
            }

            executor.shutdown();

            for (Future<int[]> future : futures) {
                try {
                    int[] result = future.get();
                    int score = result[0];
                    int moveIndex = result[1];
                    Move move = possibleMoves.get(moveIndex);

                    System.out.println("Move: " + move + ", Score: " + score + ", Depth: " + maxDepth);

                    if (score == MAX_TIME_SCORE) {
                        continue;
                    }

                    if (score > bestScore) {
                        bestScore = score;
                        CURRENT_BEST_MOVES.clear();
                        CURRENT_BEST_MOVES.add(move);
                    } else if (score == bestScore) {
                        CURRENT_BEST_MOVES.add(move);
                    }
                } catch (Exception e) {
                }
            }

            if (!CURRENT_BEST_MOVES.isEmpty()) {
                BEST_MOVES.clear();
                BEST_MOVES.addAll(CURRENT_BEST_MOVES);
            }

            maxDepth++;
        }

        if (BEST_MOVES.isEmpty()) {
            System.out.println("!!!!!!!!!!!! RANDOM MOVE !!!!!!!!!!!!");
            possibleMoves = BOARD.getPossibleMoves(PLAYER);
            System.out.println("Possible moves: " + possibleMoves);
            BEST_MOVES.add(possibleMoves.get(RANDOM.nextInt(possibleMoves.size())));
        }

        return BEST_MOVES;
    }

    private int negamax(Player player, Board board, int alpha, int beta, int currentDepth, long startTime) {
        List<Move> possibleMoves = board.getPossibleMoves(player);
        int score = board.evaluate(player);

        if (score >= WIN_SCORE || score <= LOSS_SCORE || possibleMoves.isEmpty() || currentDepth <= 0 || (System.currentTimeMillis() - startTime >= MAX_TIME_MILLIS)) {
            return score;
        }

        int maxScore = Integer.MIN_VALUE;

        for (Move move : possibleMoves) {
            if (System.currentTimeMillis() - startTime >= MAX_TIME_MILLIS) {
                return MAX_TIME_SCORE;
            }

            board.play(move);
            int value = -negamax(player.getOpponent(), board, -beta, -alpha, currentDepth - 1, startTime);
            board.undo();

            if (value == MAX_TIME_SCORE) {
                return MAX_TIME_SCORE;
            }

            maxScore = Math.max(maxScore, value);
            alpha = Math.max(alpha, maxScore);

            if (alpha >= beta) {
                break;
            }
        }

        return maxScore;
    }
}
