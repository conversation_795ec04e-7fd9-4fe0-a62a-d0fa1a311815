package log320.evaluators;

import log320.Board;
import log320.Move;
import log320.Player;

import java.util.ArrayList;

import static log320.Const.LOSS_SCORE;
import static log320.Const.WIN_SCORE;

public class TestEvaluator implements IEvaluator {
    @Override
    public int evaluate(Board board, Player player) {
        ArrayList<Move> myMoves = board.getPossibleMoves(player);
        ArrayList<Move> opponentMoves = board.getPossibleMoves(player.getOpponent());

        // Conditions de victoire
        if (myMoves.isEmpty()) {
            return LOSS_SCORE;
        } else if (opponentMoves.isEmpty()) {
            return WIN_SCORE;
        }

        for (int row = 0; row < 8; row++) {
            for (int col = 0; col < 8; col += 7) {
                if (col == player.getOpponent().getWinningCol() && (board.getBoard()[row][col] == player.getOpponent().getPusher() || board.getBoard()[row][col] == player.getOpponent().getPawn())) {
                    return LOSS_SCORE;
                } else if (col == player.getWinningCol() && (board.getBoard()[row][col] == player.getPusher() || board.getBoard()[row][col] == player.getPawn())) {
                    return WIN_SCORE;
                }
            }
        }

        int score = 0;

        // Différences de mouvements
        score += (myMoves.size() - opponentMoves.size()) * 100;

        for (Move move : myMoves) {
            int row = move.getToRow();
            int col = move.getToCol();

            // Distance
            if (board.getBoard()[row][col] == player.getPusher()) {
                int distanceToGoal = Math.abs(move.getFromCol() - player.getWinningCol());
                score += (7 - distanceToGoal) * 10;

                // Contrôle du centre
                if (row > 1 && row < 6) {
                    score += 35;
                }
            }

            if (board.getBoard()[row][col + player.getForwardColumn()] == player.getOpponent().getPusher()) {
                score += 100;
            }

            // Peux capturer
            if (move.isDiagonal()) {
                if (board.getBoard()[move.getToRow()][move.getToCol()] == player.getOpponent().getPusher()) {
                    score += 200;
                } else if (board.getBoard()[move.getToRow()][move.getToCol()] == player.getOpponent().getPawn()) {
                    score += 90;
                }
            }
        }

        for (Move move : opponentMoves) {
            int row = move.getFromRow();
            int col = move.getFromCol();

            // Distance
            if (board.getBoard()[row][col] == player.getOpponent().getPusher()) {
                int distanceToGoal = Math.abs(move.getFromCol() - player.getOpponent().getWinningCol());
                score -= (7 - distanceToGoal) * 10;
            }

            // Peux capturer
            if (move.isDiagonal()) {
                if (board.getBoard()[move.getToRow()][move.getToCol()] == player.getPusher()) {
                    score -= 200;
                } else if (board.getBoard()[move.getToRow()][move.getToCol()] == player.getPawn()) {
                    score -= 90;
                }
            }
        }

        int[][] forwardOffsets = {
                {0, player.getOpponent().getForwardColumn()},
                {-1, player.getOpponent().getForwardColumn()},
                {1, player.getOpponent().getForwardColumn()}
        };

        // Clear path
        for (Move move : myMoves) {
            int row = move.getFromRow();
            int col = move.getFromCol();

            if (board.getBoard()[row][col] == player.getPusher()) {
                for (int[] offset : forwardOffsets) {
                    int newRow = row + offset[0];
                    int newCol = col + offset[1];
                    boolean clearPath = true;

                    while (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
                        if (board.getBoard()[newRow][newCol] != 0) {
                            clearPath = false;
                            break;
                        }

                        newRow += offset[0];
                        newCol += offset[1];
                    }

                    if (clearPath && (newCol <= 0 || newCol >= 7)) {
                        score += 200;
                    }
                }
            }
        }

        for (Move move : opponentMoves) {
            int row = move.getFromRow();
            int col = move.getFromCol();

            if (board.getBoard()[row][col] == player.getOpponent().getPusher()) {
                for (int[] offset : forwardOffsets) {
                    int newRow = row + offset[0];
                    int newCol = col + offset[1];
                    boolean clearPath = true;

                    while (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
                        if (board.getBoard()[newRow][newCol] != 0) {
                            clearPath = false;
                            break;
                        }

                        newRow += offset[0];
                        newCol += offset[1];
                    }

                    if (clearPath && (newCol <= 0 || newCol >= 7)) {
                        score -= 200;
                    }
                }
            }
        }

        return score;
    }
}
