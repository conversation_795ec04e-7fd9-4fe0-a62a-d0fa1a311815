package log320.evaluators;

import log320.Board;
import log320.Player;

import static log320.Const.LOSS_SCORE;
import static log320.Const.WIN_SCORE;

public class NegamaxEvaluator implements IEvaluator {
    @Override
    public int evaluate(Board board, Player player) {
        int myPushersCount = 0;
        int myPawnsCount = 0;
        int opponentPushersCount = 0;
        int opponentPawnsCount = 0;

        // Conditions de victoire
        for (int row = 0; row < 8; row++) {
            for (int col = 0; col < 8; col++) {
                if (board.getBoard()[row][col] == player.getOpponent().getPusher()) {
                    opponentPushersCount++;

                    if (col == player.getOpponent().getWinningCol()) {
                        return LOSS_SCORE;
                    }
                } else if (board.getBoard()[row][col] == player.getOpponent().getPawn()) {
                    opponentPawnsCount++;

                    if (col == player.getOpponent().getWinningCol()) {
                        return LOSS_SCORE;
                    }
                } else if (board.getBoard()[row][col] == player.getPusher()) {
                    myPushersCount++;

                    if (col == player.getWinningCol()) {
                        return WIN_SCORE;
                    }
                } else if (board.getBoard()[row][col] == player.getPawn()) {
                    myPawnsCount++;

                    if (col == player.getWinningCol()) {
                        return WIN_SCORE;
                    }
                }
            }
        }

        if (myPushersCount == 0) {
            return LOSS_SCORE;
        } else if (opponentPushersCount == 0) {
            return WIN_SCORE;
        }

        // 100 pour pusher
        // 25 pour pion
        int score = 100 * (myPushersCount - opponentPushersCount) + 25 * (myPawnsCount - opponentPawnsCount);

        return score;
    }
}
